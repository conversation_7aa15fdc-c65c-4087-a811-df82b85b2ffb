import 'package:flutter/material.dart';

void main() {
  runApp(AccountingApp());
}

class AccountingApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'برنامج محاسبة بسيط',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Arial',
      ),
      home: AccountingHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AccountingHomePage extends StatefulWidget {
  @override
  _AccountingHomePageState createState() => _AccountingHomePageState();
}

class _AccountingHomePageState extends State<AccountingHomePage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();

  String _transactionType = 'مدين';
  List<Transaction> _transactions = [];
  double _balance = 0.0;

  void _addTransaction() {
    String name = _nameController.text.trim();
    double? parsedAmount = double.tryParse(_amountController.text);

    if (name.isEmpty || parsedAmount == null) {
      _showAlert('يرجى إدخال اسم صحيح والمبلغ');
      return;
    }

    double amount = parsedAmount;

    if (_transactionType == 'دائن') {
      amount = -amount;
    }

    setState(() {
      _transactions.add(Transaction(name: name, amount: amount, date: DateTime.now()));
      _balance += amount;
      _nameController.clear();
      _amountController.clear();
    });
  }

  void _showAlert(String msg) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text('تنبيه'),
        content: Text(msg),
        actions: [
          TextButton(onPressed: () => Navigator.of(ctx).pop(), child: Text('حسناً')),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text('برنامج محاسبة بسيط'),
        ),
        body: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              TextField(
                controller: _nameController,
                decoration: InputDecoration(labelText: 'اسم العميل'),
              ),
              SizedBox(height: 10),
              TextField(
                controller: _amountController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(labelText: 'المبلغ'),
              ),
              SizedBox(height: 10),
              DropdownButton<String>(
                value: _transactionType,
                items: ['مدين', 'دائن']
                    .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (val) {
                  if (val != null) {
                    setState(() {
                      _transactionType = val;
                    });
                  }
                },
              ),
              SizedBox(height: 10),
              ElevatedButton(
                onPressed: _addTransaction,
                child: Text('إضافة'),
              ),
              SizedBox(height: 20),
              Text(
                'الرصيد الحالي: ${_balance.toStringAsFixed(2)} جنيه',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Expanded(
                child: _transactions.isEmpty
                    ? Center(child: Text('لا توجد معاملات'))
                    : ListView.builder(
                        itemCount: _transactions.length,
                        itemBuilder: (ctx, i) {
                          final tx = _transactions[i];
                          return Card(
                            elevation: 3,
                            margin: EdgeInsets.symmetric(vertical: 6),
                            child: ListTile(
                              title: Text(tx.name),
                              subtitle: Text(
                                '${tx.date.year}/${tx.date.month.toString().padLeft(2, '0')}/${tx.date.day.toString().padLeft(2, '0')} '
                                '${tx.date.hour.toString().padLeft(2, '0')}:${tx.date.minute.toString().padLeft(2, '0')}',
                              ),
                              trailing: Text(
                                tx.amount.toStringAsFixed(2),
                                style: TextStyle(
                                  color: tx.amount >= 0 ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class Transaction {
  final String name;
  final double amount;
  final DateTime date;

  Transaction({
    required this.name,
    required this.amount,
    required this.date,
  });
}
